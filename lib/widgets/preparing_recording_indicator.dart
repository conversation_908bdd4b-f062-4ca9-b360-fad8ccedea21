import 'package:flutter/material.dart';

class PreparingRecordingIndicator extends StatelessWidget {
  final bool isPreparingRecording;

  const PreparingRecordingIndicator({
    super.key,
    required this.isPreparingRecording,
  });

  @override
  Widget build(BuildContext context) {
    if (!isPreparingRecording) {
      return SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              'Preparing recording...',
              style: TextStyle(
                color: Colors.blue.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
