part of 'record_bloc.dart';

@immutable
class RecordState {
  final bool isRecording;
  final bool isRecordingCompleted;
  final String? recordingPath;
  final int recordingDuration;
  final String selectedFolder;
  final bool isInitialized;
  final bool hasRecoveredRecording;
  final bool isUploadSuccess;
  final bool isUploadFailed;
  final bool isUploading;
  final double uploadProgress;
  final String? uploadError;
  final bool isPreparingRecording;

  const RecordState({
    this.isRecording = false,
    this.isRecordingCompleted = false,
    this.recordingPath,
    this.recordingDuration = 0,
    this.selectedFolder = "Select Folder",
    this.isInitialized = false,
    this.hasRecoveredRecording = false,
    this.isUploadFailed = false,
    this.isUploadSuccess = false,
    this.isUploading = false,
    this.uploadProgress = 0.0,
    this.uploadError,
    this.isPreparingRecording = false,
  });

  RecordState copyWith({
    bool? isRecording,
    bool? isRecordingCompleted,
    String? recordingPath,
    int? recordingDuration,
    String? selectedFolder,
    bool? isInitialized,
    bool? hasRecoveredRecording,
    bool? isUploadSuccess,
    bool? isUploadFailed,
    bool? isUploading,
    double? uploadProgress,
    String? uploadError,
    bool? isPreparingRecording,
  }) {
    return RecordState(
      isRecording: isRecording ?? this.isRecording,
      isRecordingCompleted: isRecordingCompleted ?? this.isRecordingCompleted,
      recordingPath: recordingPath ?? this.recordingPath,
      recordingDuration: recordingDuration ?? this.recordingDuration,
      selectedFolder: selectedFolder ?? this.selectedFolder,
      isInitialized: isInitialized ?? this.isInitialized,
      hasRecoveredRecording:
          hasRecoveredRecording ?? this.hasRecoveredRecording,
      isUploadSuccess: isUploadSuccess ?? this.isUploadSuccess,
      isUploadFailed: isUploadFailed ?? this.isUploadFailed,
      isUploading: isUploading ?? this.isUploading,
      uploadProgress: uploadProgress ?? this.uploadProgress,
      uploadError: uploadError ?? this.uploadError,
      isPreparingRecording: isPreparingRecording ?? this.isPreparingRecording,
    );
  }
}
