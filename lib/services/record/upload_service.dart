import 'dart:async';
import 'dart:io';

import 'package:firebase_storage/firebase_storage.dart';
import 'package:sluqe/models/folder.dart';
import 'package:uuid/uuid.dart';

class UploadService {
  Future<void> uploadRecording(
    String path,
    String userId,
    Folder? folder, {
    Function(double)? onProgress,
  }) async {
    print('🔄 UploadService: Starting upload process');
    print('📁 WAV file: $path');
    print('👤 User ID: $userId');
    print('📂 Folder: ${folder?.path ?? 'null'}');

    final audioFile = File(path);
    final audioId = Uuid().v4(); // Generate ID once
    print('🆔 Generated audio ID: $audioId');

    final storageRef = FirebaseStorage.instance
        .ref()
        .child('audios')
        .child(userId)
        .child(audioId);
    print('📍 Firebase storage path: audios/$userId/$audioId');

    if (!await audioFile.exists()) {
      throw Exception('File does not exist: $path');
    }

    final fileSize = await audioFile.length();
    print('📊 File size: ${fileSize} bytes');

    // Use file-based upload instead of loading into memory
    int retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        await _performFileUpload(storageRef, audioFile, folder, onProgress);
        return;
      } catch (e) {
        retryCount++;
        print('❌ Upload attempt $retryCount failed: $e');
        if (retryCount < maxRetries) {
          print('⏳ Retrying in ${2 * retryCount} seconds...');
          await Future.delayed(Duration(seconds: 2 * retryCount));
        }
      }
    }
    throw Exception('Upload failed after $maxRetries attempts');
  }

  Future<void> _performFileUpload(
    Reference storageRef,
    File audioFile,
    Folder? folder,
    Function(double)? onProgress,
  ) async {
    try {
      // Use putFile instead of putData to avoid loading entire file into memory
      final uploadTask = storageRef.putFile(
        audioFile,
        SettableMetadata(
          contentType: 'audio/wav',
          customMetadata: folder != null && folder.id != null ? {'folderId': folder.id!} : null,
        ),
      );

      if (onProgress != null) {
        uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
          final progress = snapshot.bytesTransferred / snapshot.totalBytes;
          onProgress(progress);
        });
      }

      await uploadTask.timeout(
        const Duration(minutes: 5), // Increased timeout for large files
        onTimeout: () {
          throw Exception('Upload timeout after 5 minutes');
        },
      );

      print('✅ Upload completed successfully');
    } on FirebaseException {
      rethrow;
    }
  }


}
