import 'dart:async';
import 'package:audio_service/audio_service.dart';
import 'package:flutter/material.dart';

class ProfessionalNotificationService extends BaseAudioHandler {
  static ProfessionalNotificationService? _instance;
  static ProfessionalNotificationService get instance =>
      _instance ??= ProfessionalNotificationService._();

  ProfessionalNotificationService._();

  bool _isRecording = false;
  Duration _recordingDuration = Duration.zero;
  Timer? _animationTimer;
  int _waveAnimationFrame = 0;

  Future<void> initialize() async {
    print('ProfessionalNotificationService: Initializing...');

    // Initialize audio service
    try {
      await AudioService.init(
        builder: () => this,
        config: const AudioServiceConfig(
          androidNotificationChannelId: 'com.sluqe.recording',
          androidNotificationChannelName: 'Recording',
          androidNotificationChannelDescription:
              'Audio Recording Notifications',
          androidNotificationOngoing: true,
          androidShowNotificationBadge: true,
          androidNotificationIcon: 'drawable/ic_notification',
          fastForwardInterval: Duration.zero,
          rewindInterval: Duration.zero,
        ),
      );
    } catch (e) {
      print('AudioService already initialized or error: $e');
    }
  }

  Future<void> startRecordingNotification({
    required VoidCallback onStopPressed,
  }) async {
    _isRecording = true;
    _recordingDuration = Duration.zero;

    // Create the initial media item with proper styling
    final mediaItem = MediaItem(
      id: 'recording_session',
      title: 'Recording Audio',
      artist: 'Sluqe • 00:00 ●○○',
      album: 'Recording Session',
      duration: null,
      artUri: null, // Let Android use the notification icon from config
      extras: {'isRecording': true, 'onStopPressed': onStopPressed},
    );

    this.mediaItem.add(mediaItem);

    // Set up the playback state with recording controls
    playbackState.add(
      PlaybackState(
        controls: [
          MediaControl.stop, // Stop icon (■) but triggers pause action
        ],
        systemActions: {
          MediaAction.pause, // Pause action that triggers stop functionality
        },
        playing: true,
        processingState: AudioProcessingState.ready,
        updatePosition: Duration.zero,
        speed: 1.0,
      ),
    );

    // Start the wave animation
    _startWaveAnimation();
  }

  void _startWaveAnimation() {
    _animationTimer?.cancel();
    _animationTimer = Timer.periodic(const Duration(milliseconds: 500), (
      timer,
    ) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }

      _waveAnimationFrame = (_waveAnimationFrame + 1) % 4;
      _updateNotificationWithAnimation();
    });
  }

  void _updateNotificationWithAnimation() {
    final currentItem = mediaItem.value;
    if (currentItem == null || !_isRecording) return;

    // Create animated wave indicators
    String waveIndicator = _getWaveIndicator(_waveAnimationFrame);
    String formattedDuration = _formatDuration(_recordingDuration);

    final updatedItem = currentItem.copyWith(
      artist: 'Sluqe • $formattedDuration $waveIndicator',
    );

    mediaItem.add(updatedItem);
  }

  String _getWaveIndicator(int frame) {
    switch (frame) {
      case 0:
        return '●○○';
      case 1:
        return '○●○';
      case 2:
        return '○○●';
      case 3:
        return '○●○';
      default:
        return '●○○';
    }
  }

  void updateRecordingDuration(Duration duration) {
    if (!_isRecording) return;

    _recordingDuration = duration;

    // Update the notification immediately with new duration
    _updateNotificationWithAnimation();
  }

  Future<void> stopRecordingNotification() async {
    _isRecording = false;
    _animationTimer?.cancel();

    // Clear the notification
    playbackState.add(
      PlaybackState(
        controls: [],
        systemActions: {},
        playing: false,
        processingState: AudioProcessingState.idle,
      ),
    );

    mediaItem.add(null);

    print('ProfessionalNotificationService: Recording notification stopped');
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  @override
  Future<void> stop() async {
    final currentItem = mediaItem.value;
    if (currentItem?.extras?['onStopPressed'] != null) {
      final onStopPressed =
          currentItem!.extras!['onStopPressed'] as VoidCallback;
      onStopPressed();
    }
    await stopRecordingNotification();
  }

  @override
  Future<void> pause() async {
    await stop();
  }

  @override
  Future<void> play() async {
    // Not applicable for recording
  }
}
