import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sluqe/services/record/background_upload_service.dart';
import 'package:sluqe/services/record/storage_service.dart';

class QueueDebugService {
  static const String _backgroundQueueKey = 'upload_queue';
  static const String _storageQueueKey = 'pending_uploads';
  static const String _activeUploadKey = 'active_upload';

  static Future<void> printQueueStatus() async {
    print('🔍 === QUEUE DEBUG STATUS ===');
    
    final prefs = await SharedPreferences.getInstance();
    
    // Check BackgroundUploadService queue
    final backgroundQueueJson = prefs.getString(_backgroundQueueKey) ?? '[]';
    final backgroundQueue = List<Map<String, dynamic>>.from(jsonDecode(backgroundQueueJson));
    print('📋 BackgroundUploadService queue: ${backgroundQueue.length} items');
    for (int i = 0; i < backgroundQueue.length; i++) {
      final item = backgroundQueue[i];
      final filePath = item['filePath'] as String;
      final fileExists = await File(filePath).exists();
      print('  $i: ${item['filePath']} (exists: $fileExists, retries: ${item['retryCount']})');
    }
    
    // Check StorageService pending uploads
    final storageQueueJson = prefs.getString(_storageQueueKey) ?? '[]';
    final storageQueue = List<Map<String, dynamic>>.from(jsonDecode(storageQueueJson));
    final pendingUploads = storageQueue.where((upload) => upload['isUploaded'] != true).toList();
    print('📋 StorageService pending uploads: ${pendingUploads.length} items');
    for (int i = 0; i < pendingUploads.length; i++) {
      final item = pendingUploads[i];
      final filePath = item['filePath'] as String;
      final fileExists = await File(filePath).exists();
      print('  $i: ${item['filePath']} (exists: $fileExists, retries: ${item['retryCount']})');
    }
    
    // Check active upload
    final activeUpload = prefs.getString(_activeUploadKey);
    print('🔄 Active upload: ${activeUpload != null ? 'YES' : 'NO'}');
    if (activeUpload != null) {
      final activeData = jsonDecode(activeUpload);
      print('  Active: ${activeData['filePath']}');
    }
    
    print('🔍 === END QUEUE DEBUG ===');
  }

  static Future<void> clearAllQueues() async {
    print('🧹 Clearing all queues...');
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_backgroundQueueKey);
    await prefs.remove(_storageQueueKey);
    await prefs.remove(_activeUploadKey);
    print('✅ All queues cleared');
  }

  static Future<void> forceProcessQueue() async {
    print('🚀 Force processing queue...');
    final backgroundService = BackgroundUploadService();
    await backgroundService.resumeProcessing();
    print('✅ Queue processing triggered');
  }

  static Future<bool> hasAnyPendingUploads() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Check background queue
    final backgroundQueueJson = prefs.getString(_backgroundQueueKey) ?? '[]';
    final backgroundQueue = List<Map<String, dynamic>>.from(jsonDecode(backgroundQueueJson));
    
    // Check storage queue
    final storageQueueJson = prefs.getString(_storageQueueKey) ?? '[]';
    final storageQueue = List<Map<String, dynamic>>.from(jsonDecode(storageQueueJson));
    final pendingUploads = storageQueue.where((upload) => upload['isUploaded'] != true).toList();
    
    return backgroundQueue.isNotEmpty || pendingUploads.isNotEmpty;
  }
}
