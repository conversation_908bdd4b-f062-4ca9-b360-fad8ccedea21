import 'dart:async';
import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';

class RecordingAudioHandler extends BaseAudioHandler {
  static const String _recordingId = 'recording_session';
  Timer? _updateTimer;
  Function()? _onStopCallback;

  Duration _recordingDuration = Duration.zero;
  bool _isRecording = false;

  @override
  Future<void> prepare() async {
    try {
      final session = await AudioSession.instance;
      await session.configure(
        AudioSessionConfiguration(
          avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
          avAudioSessionCategoryOptions:
              AVAudioSessionCategoryOptions.allowBluetooth |
              AVAudioSessionCategoryOptions.defaultToSpeaker,
          avAudioSessionMode: AVAudioSessionMode.defaultMode,
          avAudioSessionRouteSharingPolicy:
              AVAudioSessionRouteSharingPolicy.defaultPolicy,
          avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
          androidAudioAttributes: const AndroidAudioAttributes(
            contentType: AndroidAudioContentType.speech,
            flags: AndroidAudioFlags.none,
            usage: AndroidAudioUsage.voiceCommunication,
          ),
          androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
          androidWillPauseWhenDucked: false,
        ),
      );

      await super.prepare();
      print('AudioServiceHandler prepared successfully with audio session');
    } catch (e) {
      print('AudioServiceHandler prepare failed: $e');
    }
  }

  // Override all unwanted media actions to prevent them from appearing
  @override
  Future<void> play() async {
    // Do nothing - we don't want play functionality for recording
  }

  @override
  Future<void> seek(Duration position) async {
    // Do nothing - no seek functionality for recording
  }

  @override
  Future<void> skipToNext() async {
    // Do nothing - no skip functionality for recording
  }

  @override
  Future<void> skipToPrevious() async {
    // Do nothing - no skip functionality for recording
  }

  @override
  Future<void> fastForward() async {
    // Do nothing - no fast forward for recording
  }

  @override
  Future<void> rewind() async {
    // Do nothing - no rewind for recording
  }

  Future<void> startRecording({required Function() onStopPressed}) async {
    await prepare();

    try {
      final session = await AudioSession.instance;
      await session.setActive(true);
      print('AudioServiceHandler: Audio session activated');
    } catch (e) {
      print('AudioServiceHandler: Failed to activate audio session: $e');
    }

    _onStopCallback = onStopPressed;
    _isRecording = true;
    _recordingDuration = Duration.zero;

    final mediaItem = MediaItem(
      id: _recordingId,
      title: 'Recording Audio', // Fixed title - never changes
      artist: 'Sluqe Recording', // Fixed artist - never changes
      album: 'Recording Session', // Fixed album - never changes
      duration: null, // No fixed duration
      artUri: null, // No artwork
      extras: {'isRecording': true},
    );

    this.mediaItem.add(mediaItem);
    print('AudioServiceHandler: Media item set - ${mediaItem.title}');

    // Create platform-specific playback state with stop icon that triggers pause action
    final playbackStateObj = PlaybackState(
      controls: [
        MediaControl.stop, // Stop button (■ icon)
      ],
      systemActions: {
        MediaAction.pause, // Pause action that triggers stop functionality
      },
      playing: true, // Always true for recording
      processingState: AudioProcessingState.ready,
      updatePosition: Duration.zero, // Start at zero
      bufferedPosition: Duration.zero,
      speed: 1.0, // Normal speed for smooth updates
    );

    playbackState.add(playbackStateObj);
    print('AudioServiceHandler: Playback state set - recording mode');

    _startDurationUpdates();

    print('AudioServiceHandler: Recording session started successfully');
  }

  void updateRecordingDuration(Duration duration) {
    if (!_isRecording) return;

    _recordingDuration = duration;

    // Use PlaybackState.updatePosition for real-time duration updates
    // This is the correct way according to audio_service documentation
    playbackState.add(
      PlaybackState(
        controls: [MediaControl.stop], // Stop button (■ icon)
        systemActions: {
          MediaAction.stop,
        }, // Stop action triggered by stop button
        playing: true,
        processingState: AudioProcessingState.ready,
        updatePosition: duration, // ✅ This shows duration in notification
        bufferedPosition: duration,
        speed: 1.0,
      ),
    );
  }

  Future<void> stopRecording() async {
    _isRecording = false;
    _updateTimer?.cancel();
    _updateTimer = null;

    try {
      final session = await AudioSession.instance;
      await session.setActive(false);
      print('AudioServiceHandler: Audio session deactivated');
    } catch (e) {
      print('AudioServiceHandler: Failed to deactivate audio session: $e');
    }

    playbackState.add(
      PlaybackState(
        controls: [],
        systemActions: {},
        playing: false,
        processingState: AudioProcessingState.idle,
        updatePosition: Duration.zero,
      ),
    );

    mediaItem.add(null);
  }

  @override
  Future<void> stop() async {
    await stopRecording();
    _onStopCallback?.call();
    await super.stop();
  }

  @override
  Future<void> pause() async {
    await stop();
  }

  void _startDurationUpdates() {
    _updateTimer?.cancel();
    _updateTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }

      // This is the missing piece! Update the duration internally
      _recordingDuration = Duration(seconds: _recordingDuration.inSeconds + 1);

      // Update the notification with new duration
      updateRecordingDuration(_recordingDuration);
    });
  }

  @override
  Future<void> onTaskRemoved() async {
    await stop();
    await super.onTaskRemoved();
  }

  @override
  Future<void> onNotificationDeleted() async {
    await stop();
    await super.onNotificationDeleted();
  }

  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  bool get isRecording => _isRecording;

  Duration get recordingDuration => _recordingDuration;
}
